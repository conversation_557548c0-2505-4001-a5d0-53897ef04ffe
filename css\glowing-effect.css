/* Effet néon laser pour les cartes - Copie exacte du code qui fonctionnait */

@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

:root {
  --gemini-blue-light: #2190F6;
  --gemini-blue-purple: #6689EF;
  --gemini-purple: rgb(77, 70, 175);
  --gemini-salmon: rgb(235, 73, 114);
}

/* Classe utilitaire pour l'effet néon laser - EXACTEMENT comme glowing-card */
.glowing-effect {
  position: relative;
}

.glowing-effect::before,
.glowing-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.glowing-effect::before,
.glowing-effect::after {
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
}

.glowing-effect::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.glowing-effect::after {
  filter: brightness(2.5);
}

.glowing-effect::before,
.glowing-effect::after {
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.glowing-effect:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.glowing-effect:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

@keyframes spin {
  from { --angle: 0deg; }
  to { --angle: 360deg; }
}

/* L'effet reste en arrière-plan grâce au z-index: -1 des pseudo-éléments */

/* Classe générale combinée pour faciliter l'usage */
.neon-card {
  position: relative;
  background-color: #1A2035; /* bg-brand-surface */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 2rem; /* p-8 */
  border: 1px solid #1A2035; /* border border-brand-surface */
}

.neon-card::before,
.neon-card::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.neon-card::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.neon-card::after {
  filter: brightness(2.5);
}

.neon-card:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.neon-card:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

/* Variante petite pour les cartes plus compactes */
.neon-card-sm {
  position: relative;
  background-color: #1A2035; /* bg-brand-surface */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1.5rem; /* p-6 */
  border: 1px solid #1A2035; /* border border-brand-surface */
}

.neon-card-sm::before,
.neon-card-sm::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
}

.neon-card-sm::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: translate(-50%, -50%) scale(1.01);
}

.neon-card-sm::after {
  filter: brightness(2.5);
}

.neon-card-sm:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.neon-card-sm:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

/* Classe spéciale pour les cartes de portfolio avec images */
.neon-card-portfolio {
  position: relative;
  background-color: #1A2035; /* bg-brand-surface */
  border-radius: 0.5rem; /* rounded-lg */
  border: 1px solid #1A2035; /* border border-brand-surface */
  overflow: hidden;
  /* Pas de padding ici car géré par les enfants */
}

.neon-card-portfolio::before,
.neon-card-portfolio::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2; /* Bien en arrière-plan */
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background-image: conic-gradient(
    from var(--angle),
    transparent 0%,
    transparent 60%,
    var(--gemini-salmon),
    var(--gemini-purple),
    var(--gemini-blue-purple),
    var(--gemini-blue-light)
  );
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
  pointer-events: none; /* Permet les interactions avec le contenu */
}

.neon-card-portfolio::before {
  filter: blur(0.2rem) brightness(2.5);
  transform: scale(1.01);
}

.neon-card-portfolio::after {
  filter: brightness(2.5);
}

.neon-card-portfolio:hover::after {
  opacity: 1;
  animation: spin 5s linear infinite;
}

.neon-card-portfolio:hover::before {
  opacity: 0.8;
  animation: spin 5s linear infinite;
}

/* Assurer que le contenu des cartes portfolio reste au-dessus */
.neon-card-portfolio > * {
  position: relative;
  z-index: 10;
}

/* Assurer que les images et liens restent bien au-dessus */
.neon-card-portfolio img,
.neon-card-portfolio a,
.neon-card-portfolio span,
.neon-card-portfolio h3,
.neon-card-portfolio p {
  position: relative;
  z-index: 10;
}
