
import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Project } from '../types';

const projectsData: Project[] = [
    {
        title: 'Advisor Nutrition',
        category: 'Frontend',
        description: 'Application de gestion intelligente des listes de courses et planification nutritionnelle avec IA',
        imageUrl: '/assets/portfolio/projects/Advisor-Nutrition/Advisor-Nutrition.jpg',
        tags: ['JavaScript ES6', 'Tailwind CSS', 'OpenAI API'],
        link: '/projects/advisor-nutrition'
    },
    {
        title: 'Assistant Parcours & Découverte',
        category: 'IA',
        description: 'Application d\'optimisation de parcours pour livraisons et découvertes touristiques avec IA',
        imageUrl: '/assets/portfolio/projects/Assistant-Parcours-Decouverte/assistant-parcours-decouverte.jpg',
        tags: ['React 18', 'TypeScript', 'Google Maps API'],
        link: '/projects/assistant-parcours'
    },
    {
        title: 'BudgetSage',
        category: 'Frontend',
        description: 'Application web de gestion personnelle des finances avec visualisations avancées',
        imageUrl: '/assets/portfolio/projects/budgetwise/budgetwise.jpg',
        tags: ['Next.js', 'React', 'Shadcn/ui'],
        link: '/projects/budget-sage'
    },
    {
        title: 'Dashboard Météo V2',
        category: 'Frontend',
        description: 'Dashboard météorologique intelligent avec intégration IA Gemini et données temps réel',
        imageUrl: '/assets/portfolio/projects/dashboard-meteo-v2/dashboard-meteo-v2.jpg',
        tags: ['React', 'Gemini API', 'Weather API'],
        link: '/projects/dashboard-meteo'
    },
    {
        title: 'Developer Inspector Mode',
        category: 'Outils',
        description: 'Composant d\'inspection web avec annotations IA et intégration Context7',
        imageUrl: '/assets/portfolio/projects/DeveloperInspectorMode_Module/Developer-Inspector-Mode.jpg',
        tags: ['JavaScript', 'Gemini 2.0', 'Context7'],
        link: '/projects/developer-inspector'
    },
    {
        title: 'PromptFlow Pro Dashboard',
        category: 'IA',
        description: 'Application de transformation de texte en tableaux de bord analytiques visuels',
        imageUrl: '/assets/portfolio/projects/promptflow-dash-wizard/promptflow-dashboard..jpg',
        tags: ['React', 'Chart.js', 'ApexCharts'],
        link: '/projects/promptflow-dashboard'
    },
    {
        title: 'Violet Rikita',
        category: 'IA',
        description: 'Assistant créatif IA pour génération de contenu et optimisation des workflows créatifs',
        imageUrl: '/assets/fond-de-technologie-05.jpg',
        tags: ['IA Générative', 'Workflow', 'Design'],
        link: '#',
        status: 'En cours'
    },
];

const categories = ['Tous les projets', 'Développement IA', 'Frontend Interactif', 'Automatisation & Outils'];

const Portfolio: React.FC = () => {
    const [filter, setFilter] = useState('Tous les projets');

    const filteredProjects = useMemo(() => {
        if (filter === 'Tous les projets') return projectsData;
        if (filter === 'Développement IA') return projectsData.filter(p => p.category === 'IA');
        if (filter === 'Frontend Interactif') return projectsData.filter(p => p.category === 'Frontend');
        if (filter === 'Automatisation & Outils') return projectsData.filter(p => p.category === 'Outils');
        return [];
    }, [filter]);

    return (
        <div className="container mx-auto px-6 py-20">
            <section className="text-center max-w-4xl mx-auto">
                <h1 className="text-5xl font-bold mb-4">Mes Réalisations</h1>
                <p className="text-xl text-brand-muted">
                    Découvrez une sélection de mes projets les plus marquants, alliant innovation technique et design créatif.
                </p>
            </section>

            <section className="mt-16">
                <div className="flex justify-center flex-wrap gap-4 mb-12">
                    {categories.map(cat => (
                        <button
                            key={cat}
                            onClick={() => setFilter(cat)}
                            className={`px-6 py-2 rounded-full font-semibold transition-all duration-300 ${filter === cat ? 'bg-gradient-to-r from-brand-blue to-brand-purple text-white' : 'bg-brand-surface text-brand-muted hover:bg-brand-surface-light hover:text-white'}`}
                        >
                            {cat}
                        </button>
                    ))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {filteredProjects.map(project => (
                        <div key={project.title} className="neon-card-portfolio group hover:border-brand-purple transition-all transform hover:-translate-y-2">
                            <div className="relative">
                                <img
                                    src={project.imageUrl}
                                    alt={project.title}
                                    className="w-full h-48 object-cover"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = `https://placehold.co/600x400/1a202c/4a5568?text=${encodeURIComponent(project.title)}`;
                                    }}
                                />
                                {project.status && <span className="absolute top-2 right-2 bg-yellow-500 text-black text-xs font-bold px-2 py-1 rounded">{project.status}</span>}
                            </div>
                            <div className="p-6">
                                <h3 className="text-xl font-bold text-white mb-2">{project.title}</h3>
                                <p className="text-brand-muted mb-4 text-sm">{project.description}</p>
                                <div className="flex flex-wrap gap-2 mb-4">
                                    {project.tags.map(tag => (
                                        <span key={tag} className="bg-brand-surface text-brand-blue text-xs font-medium px-2 py-1 rounded">{tag}</span>
                                    ))}
                                </div>
                                <Link to={project.link} className="text-brand-purple font-semibold hover:underline">Voir le projet →</Link>
                            </div>
                        </div>
                    ))}
                </div>
            </section>
        </div>
    );
};

export default Portfolio;