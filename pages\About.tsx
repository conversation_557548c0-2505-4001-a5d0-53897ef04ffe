import React from 'react';
import { TechStacksDemo } from '../components/TechStacksDemo';

const About: React.FC = () => {

    const journey = [
        {
            icon: (
                <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="gemini-grad-1" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#1A3452" />
                            <stop offset="25%" stopColor="#2190F6" />
                            <stop offset="50%" stopColor="#6689EF" />
                            <stop offset="75%" stopColor="#8D86ED" />
                            <stop offset="100%" stopColor="#AE87F3" />
                        </linearGradient>
                    </defs>
                    <path d="M22 10v6M2 10l10-5 10 5-10 5z" stroke="url(#gemini-grad-1)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="m6 12 4 4 4-4" stroke="url(#gemini-grad-1)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            title: 'Ingénieur Prompt & Développeur d\'Interfaces',
            period: '2022 - AUJ.',
            description: 'Prototypage d\'interfaces pour applications, ingénierie inversée, et conception de solutions web combinant l\'IA (Gemini) avec des animations interactives (GSAP).'
        },
        {
            icon: (
                <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="gemini-grad-2" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#1A3452" />
                            <stop offset="25%" stopColor="#2190F6" />
                            <stop offset="50%" stopColor="#6689EF" />
                            <stop offset="75%" stopColor="#8D86ED" />
                            <stop offset="100%" stopColor="#AE87F3" />
                        </linearGradient>
                    </defs>
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="url(#gemini-grad-2)" strokeWidth="2" fill="none"/>
                    <line x1="8" y1="21" x2="16" y2="21" stroke="url(#gemini-grad-2)" strokeWidth="2" strokeLinecap="round"/>
                    <line x1="12" y1="17" x2="12" y2="21" stroke="url(#gemini-grad-2)" strokeWidth="2" strokeLinecap="round"/>
                </svg>
            ),
            title: 'Développement Web & Spécialisation IA',
            period: '2015 - 2022',
            description: 'Développement (WordPress) et plongée dans l\'IA avec des réalisations majeures en ingénierie de prompts : conception d\'un template Master Prompt et traitement de documents PDF complexes.'
        },
        {
            icon: (
                <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="gemini-grad-3" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#1A3452" />
                            <stop offset="25%" stopColor="#2190F6" />
                            <stop offset="50%" stopColor="#6689EF" />
                            <stop offset="75%" stopColor="#8D86ED" />
                            <stop offset="100%" stopColor="#AE87F3" />
                        </linearGradient>
                    </defs>
                    <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" stroke="url(#gemini-grad-3)" strokeWidth="2" fill="none"/>
                    <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" stroke="url(#gemini-grad-3)" strokeWidth="2" fill="none"/>
                    <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" stroke="url(#gemini-grad-3)" strokeWidth="2" fill="none"/>
                    <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" stroke="url(#gemini-grad-3)" strokeWidth="2" fill="none"/>
                </svg>
            ),
            title: 'Micro-Entrepreneur & Développement Créatif',
            period: '2010 - 2015',
            description: 'Premières expériences entrepreneuriales et développement de projets avec la suite Adobe Creative (CS3). Création de supports visuels et de sites statiques.'
        },
        {
            icon: (
                <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="gemini-grad-4" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#1A3452" />
                            <stop offset="25%" stopColor="#2190F6" />
                            <stop offset="50%" stopColor="#6689EF" />
                            <stop offset="75%" stopColor="#8D86ED" />
                            <stop offset="100%" stopColor="#AE87F3" />
                        </linearGradient>
                    </defs>
                    <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" stroke="url(#gemini-grad-4)" strokeWidth="2" fill="none"/>
                    <path d="m9 12 2 2 4-4" stroke="url(#gemini-grad-4)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            title: 'Expériences Fondatrices',
            period: '2005 - 2010',
            description: 'Agent cynophile et logistique poids lourd. Développement d\'une forte discipline, de la rigueur et de la gestion de systèmes complexes.'
        }
    ];

    const favoriteTools = [
        { name: 'Vite.js', category: 'Build Tool' },
        { name: 'Tailwind CSS', category: 'Styling' },
        { name: 'GSAP', category: 'Animation' },
        { name: 'Firebase', category: 'Backend' },
        { name: 'Gemini API', category: 'IA' },
        { name: 'Netlify', category: 'Déploiement' },
    ];

    return (
        <div className="container mx-auto px-6 py-20">
            {/* Section Hero About */}
            <section className="text-center max-w-4xl mx-auto mb-20">
                <h1 className="text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-brand-blue to-brand-purple">
                    FlexoDiv
                </h1>
                <p className="text-xl text-brand-muted font-semibold mb-4">
                    De l'Idée à la Solution
                </p>
                <p className="text-lg text-brand-muted mb-8">
                    FlexoDiv : transformer des idées complexes en expériences numériques intuitives et percutantes.
                </p>
                <div className="bg-brand-surface/50 backdrop-blur-sm p-6 rounded-lg mb-8 border border-brand-surface">
                    <p className="text-brand-light leading-relaxed">
                        Avec un pied dans l'ingénierie du prompt et l'autre dans le développement web créatif,
                        je conçois des applications qui ne sont pas seulement fonctionnelles, mais qui offrent
                        une véritable expérience utilisateur. Mon parcours atypique est ma plus grande force :
                        il m'a appris la rigueur, l'adaptabilité et l'art de résoudre les problèmes de manière créative.
                    </p>
                </div>
            </section>

            {/* Section Qui Suis-Je */}
            <section className="mb-20">
                <h2 className="text-4xl font-bold text-center mb-12 text-white">Qui Suis-Je</h2>

                <div className="max-w-4xl mx-auto space-y-8">
                    {/* Les Fondations */}
                    <div className="bg-brand-surface/50 backdrop-blur-sm p-8 rounded-lg border border-brand-surface">
                        <h3 className="text-2xl font-bold mb-4 text-brand-purple">Les Fondations</h3>
                        <p className="text-brand-light leading-relaxed">
                            Bien avant que les lignes de code ne rythment mes journées, ma curiosité s'exprimait déjà dans des domaines bien différents.
                            Passionné de cuisine, j'ai appris à décortiquer des recettes comme des algorithmes, à comprendre l'équilibre des saveurs et la rigueur des étapes pour atteindre un résultat parfait.
                        </p>
                    </div>

                    {/* Le Déclic Technologique */}
                    <div className="bg-brand-surface/50 backdrop-blur-sm p-8 rounded-lg border border-brand-surface">
                        <h3 className="text-2xl font-bold mb-4 text-brand-purple">Le Déclic Technologique</h3>
                        <p className="text-brand-light leading-relaxed">
                            Ma rencontre avec l'intelligence artificielle, et plus particulièrement avec les modèles de langage, a été le catalyseur.
                            J'ai immédiatement perçu un potentiel qui dépassait le simple outil. C'était la promesse de pouvoir dialoguer avec la technologie,
                            de la sculpter par le langage pour en faire une extension de la créativité humaine.
                        </p>
                    </div>

                    {/* La Vision Actuelle */}
                    <div className="bg-brand-surface/50 backdrop-blur-sm p-8 rounded-lg border border-brand-surface">
                        <h3 className="text-2xl font-bold mb-4 text-brand-purple">La Vision Actuelle</h3>
                        <p className="text-brand-light leading-relaxed">
                            Aujourd'hui, ma mission est de fusionner ces mondes. Je conçois des expériences web qui ne se contentent pas d'être fonctionnelles,
                            mais qui racontent une histoire et provoquent une émotion. Je crois fermement en un web rapide, esthétique et accessible,
                            où la complexité technique reste invisible pour l'utilisateur.
                        </p>
                    </div>
                </div>
            </section>

            {/* Section Mes Parcours */}
            <section className="mb-20">
                <h2 className="text-4xl font-bold text-center mb-12 text-white">Mes Parcours</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                    {journey.map((item, index) => (
                        <div key={index} className="bg-brand-surface/50 backdrop-blur-sm p-8 rounded-lg border border-brand-surface">
                            <div className="flex items-center justify-center mx-auto mb-4">
                                {item.icon}
                            </div>
                            <h3 className="text-xl font-bold mb-2 text-white text-center">{item.title}</h3>
                            <p className="text-sm text-brand-blue font-semibold text-center mb-4">{item.period}</p>
                            <p className="text-brand-light leading-relaxed text-center">
                                {item.description}
                            </p>
                        </div>
                    ))}
                </div>
            </section>

            {/* Section L'Art de la Solution Globale */}
            <section className="mb-20">
                <h2 className="text-4xl font-bold text-center mb-8 text-white">L'Art de la Solution Globale</h2>

                <div className="bg-brand-surface/50 backdrop-blur-sm p-8 rounded-lg border border-brand-surface max-w-4xl mx-auto mb-12">
                    <p className="text-lg text-brand-light leading-relaxed text-center">
                        Je crois en une approche minimaliste et performante. Je privilégie les solutions légères,
                        rapides et maintenables. Mon objectif n'est pas d'utiliser la technologie pour la technologie,
                        mais de choisir l'outil le plus adapté pour créer une expérience utilisateur fluide et mémorable.
                    </p>
                </div>

                {/* Grille des compétences */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    {favoriteTools.map((tool, index) => (
                        <div key={index} className="bg-brand-surface/50 backdrop-blur-sm p-6 rounded-lg border border-brand-surface text-center hover:border-brand-purple transition-all">
                            <div className="text-brand-purple mb-2">
                                <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L13.5 7.5L19 9L13.5 10.5L12 16L10.5 10.5L5 9L10.5 7.5L12 2Z"/>
                                </svg>
                            </div>
                            <span className="text-white font-semibold block">{tool.name}</span>
                            <span className="text-brand-muted text-sm">{tool.category}</span>
                        </div>
                    ))}
                </div>
            </section>

            {/* Démo des stacks technologiques */}
            <section className="my-16">
                <TechStacksDemo />
            </section>

            {/* Section CTA */}
            <section className="min-h-screen flex flex-col justify-center items-center text-center">
                <h2 className="text-5xl md:text-6xl font-extrabold mb-8 text-transparent bg-clip-text bg-gradient-to-r from-[#EB4972] via-[#2190F6] to-[#6689EF] drop-shadow-lg">
                    Intéressé par votre projet ?
                </h2>
                <p className="text-2xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed font-medium drop-shadow">
                    Vous avez un projet en tête ? Discutons de la façon dont nous pouvons
                    transformer vos idées en réalité grâce à l'IA et au développement créatif.
                </p>
                <div className="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="#/contact" className="bg-gradient-to-r from-brand-blue to-brand-purple text-white px-8 py-3 rounded-lg font-semibold hover:scale-105 transition-transform">
                        Démarrer un projet
                    </a>
                    <a href="#/competences" className="bg-brand-surface text-white px-8 py-3 rounded-lg font-semibold border border-brand-surface-light hover:border-brand-purple transition-all">
                        Voir mes compétences
                    </a>
                </div>
            </section>
        </div>
    );
};

export default About;
